#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化添加好友 - 图形用户界面
功能：为main_controller.py提供可视化操作界面

核心特性：
1. 实时监控执行状态和进度
2. 可视化配置管理
3. 日志实时显示
4. 统计信息展示
5. 一键启动/停止控制
6. 多窗口状态监控

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import queue
import json
import time
from datetime import datetime
from pathlib import Path
import logging
from typing import Dict, List, Optional

# 导入主控制器
from main_controller import WeChatMainController, ExecutionStep, WindowStatus


class WeChatAutomationGUI:
    """微信自动化添加好友图形用户界面"""
    
    def __init__(self):
        """初始化现代化GUI"""
        self.root = tk.Tk()
        self.root.title("🤖 微信自动化添加好友控制台 v1.0.0")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)

        # 设置窗口居中
        self._center_window()

        # 设置现代化图标和样式
        self.setup_styles()

        # 设置窗口图标（如果有的话）
        try:
            # 可以在这里设置自定义图标
            # self.root.iconbitmap('icon.ico')
            pass
        except:
            pass
        
        # 控制器和状态
        self.controller: Optional[WeChatMainController] = None
        self.is_running = False
        self.automation_thread: Optional[threading.Thread] = None
        
        # 消息队列用于线程间通信
        self.message_queue = queue.Queue()
        
        # 状态数据
        self.execution_stats = {
            "total_contacts": 0,
            "processed_contacts": 0,
            "successful_adds": 0,
            "failed_adds": 0,
            "skipped_contacts": 0,
            "total_windows": 0,
            "completed_windows": 0,
            "start_time": None,
            "end_time": None
        }

        # 运行时参数变量
        self.runtime_params = {
            "interval_min": tk.StringVar(value="50"),
            "interval_max": tk.StringVar(value="60"),
            "daily_limit": tk.StringVar(value="200"),
            "max_per_window": tk.StringVar(value="20"),
            "morning_start": tk.StringVar(value="10:00"),
            "morning_end": tk.StringVar(value="12:00"),
            "afternoon_start": tk.StringVar(value="14:00"),
            "afternoon_end": tk.StringVar(value="23:59"),
            "rest_trigger": tk.StringVar(value="20"),
            "rest_duration": tk.StringVar(value="5")
        }

        # 时段启用/禁用状态变量
        self.time_slot_enabled = {
            "morning_enabled": tk.BooleanVar(value=True),
            "afternoon_enabled": tk.BooleanVar(value=True)
        }

        # 运行状态变量
        self.status_vars = {
            "total_progress": tk.StringVar(value="0/0 (0%)"),
            "planned_count": tk.StringVar(value="0"),
            "current_progress": tk.StringVar(value="0"),
            "success_count": tk.StringVar(value="0"),
            "error_count": tk.StringVar(value="0"),
            "current_window": tk.StringVar(value="0/0"),
            "countdown": tk.StringVar(value="0")
        }

        # 基础参数变量（显式声明以避免静态分析警告）
        # 这些变量将在create_execution_control_section中通过setattr动态创建
        self.batch_size_var: tk.StringVar
        self.delay_var: tk.StringVar
        self.excel_path_var: tk.StringVar
        
        # 创建界面
        self.create_widgets()
        self.setup_logging()
        
        # 启动消息处理
        self.process_messages()
        
        # 加载配置
        self.load_configuration()
        self.load_runtime_params_from_config()

    def _center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_styles(self):
        """设置现代化界面样式 - Material Design风格"""
        style = ttk.Style()
        style.theme_use('clam')

        # 定义现代化配色方案
        self.colors = {
            'primary': '#1976D2',      # 主色调 - 蓝色
            'primary_dark': '#1565C0', # 深蓝色
            'primary_light': '#BBDEFB', # 浅蓝色
            'secondary': '#FFC107',    # 次要色 - 琥珀色
            'success': '#4CAF50',      # 成功色 - 绿色
            'warning': '#FF9800',      # 警告色 - 橙色
            'error': '#F44336',        # 错误色 - 红色
            'info': '#2196F3',         # 信息色 - 蓝色
            'surface': '#FFFFFF',      # 表面色 - 白色
            'background': '#FAFAFA',   # 背景色 - 浅灰
            'on_surface': '#212121',   # 表面文字色 - 深灰
            'on_background': '#424242', # 背景文字色 - 中灰
            'divider': '#E0E0E0',      # 分割线色 - 浅灰
            'shadow': '#00000020'      # 阴影色 - 半透明黑
        }

        # 设置根窗口背景
        self.root.configure(bg=self.colors['background'])

        # 现代化标题样式
        style.configure('Title.TLabel',
                       font=('Segoe UI', 16, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['surface'])

        style.configure('Subtitle.TLabel',
                       font=('Segoe UI', 14, 'bold'),
                       foreground=self.colors['on_surface'],
                       background=self.colors['surface'])

        # 状态标签样式
        style.configure('Status.TLabel',
                       font=('Segoe UI', 12),
                       foreground=self.colors['on_background'],
                       background=self.colors['surface'])

        style.configure('Success.TLabel',
                       foreground=self.colors['success'],
                       font=('Segoe UI', 12, 'bold'),
                       background=self.colors['surface'])

        style.configure('Error.TLabel',
                       foreground=self.colors['error'],
                       font=('Segoe UI', 12, 'bold'),
                       background=self.colors['surface'])

        style.configure('Warning.TLabel',
                       foreground=self.colors['warning'],
                       font=('Segoe UI', 12, 'bold'),
                       background=self.colors['surface'])

        style.configure('Info.TLabel',
                       foreground=self.colors['info'],
                       font=('Segoe UI', 12, 'bold'),
                       background=self.colors['surface'])

        # 文本标签样式
        style.configure('Large.TLabel',
                       font=('Segoe UI', 12),
                       foreground=self.colors['on_surface'],
                       background=self.colors['surface'])

        style.configure('Medium.TLabel',
                       font=('Segoe UI', 11),
                       foreground=self.colors['on_background'],
                       background=self.colors['surface'])

        style.configure('Small.TLabel',
                       font=('Segoe UI', 10),
                       foreground=self.colors['on_background'],
                       background=self.colors['surface'])

        # 现代化按钮样式
        style.configure('Primary.TButton',
                       font=('Segoe UI', 11, 'bold'),
                       padding=(16, 12),
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none')

        style.configure('Secondary.TButton',
                       font=('Segoe UI', 11),
                       padding=(14, 10),
                       background=self.colors['surface'],
                       foreground=self.colors['primary'],
                       borderwidth=1,
                       focuscolor='none')

        style.configure('Large.TButton',
                       font=('Segoe UI', 11),
                       padding=(12, 10),
                       background=self.colors['surface'],
                       foreground=self.colors['on_surface'],
                       borderwidth=1,
                       focuscolor='none')

        style.configure('Medium.TButton',
                       font=('Segoe UI', 10),
                       padding=(10, 8),
                       background=self.colors['surface'],
                       foreground=self.colors['on_surface'],
                       borderwidth=1,
                       focuscolor='none')

        style.configure('Small.TButton',
                       font=('Segoe UI', 9),
                       padding=(8, 6),
                       background=self.colors['surface'],
                       foreground=self.colors['on_surface'],
                       borderwidth=1,
                       focuscolor='none')

        # 现代化输入框样式
        style.configure('Large.TEntry',
                       font=('Segoe UI', 11),
                       fieldbackground=self.colors['surface'],
                       borderwidth=2,
                       relief='solid',
                       padding=(12, 10))

        style.configure('Medium.TEntry',
                       font=('Segoe UI', 10),
                       fieldbackground=self.colors['surface'],
                       borderwidth=2,
                       relief='solid',
                       padding=(10, 8))

        # 复选框样式
        style.configure('Medium.TCheckbutton',
                       font=('Segoe UI', 10),
                       foreground=self.colors['on_surface'],
                       background=self.colors['surface'],
                       focuscolor='none')

        # 进度条样式
        style.configure('Modern.Horizontal.TProgressbar',
                       background=self.colors['primary'],
                       troughcolor=self.colors['divider'],
                       borderwidth=0,
                       lightcolor=self.colors['primary'],
                       darkcolor=self.colors['primary'])

        # LabelFrame样式
        style.configure('Modern.TLabelframe',
                       background=self.colors['surface'],
                       borderwidth=1,
                       relief='solid')

        style.configure('Modern.TLabelframe.Label',
                       font=('Segoe UI', 11, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['surface'])

        # Notebook样式
        style.configure('Modern.TNotebook',
                       background=self.colors['background'],
                       borderwidth=0)

        style.configure('Modern.TNotebook.Tab',
                       font=('Segoe UI', 11),
                       padding=(20, 12),
                       background=self.colors['surface'],
                       foreground=self.colors['on_surface'])

        # 设置悬停和激活状态
        self._setup_interactive_styles(style)

    def _setup_interactive_styles(self, style):
        """设置交互状态样式"""
        # 主按钮交互效果
        style.map('Primary.TButton',
                 background=[('active', self.colors['primary_dark']),
                           ('pressed', self.colors['primary_dark']),
                           ('disabled', '#BDBDBD')],
                 foreground=[('disabled', '#FFFFFF')])

        # 次要按钮交互效果
        style.map('Secondary.TButton',
                 background=[('active', self.colors['primary_light']),
                           ('pressed', self.colors['primary_light'])],
                 foreground=[('active', self.colors['primary_dark'])])

        # 普通按钮交互效果
        style.map('Large.TButton',
                 background=[('active', self.colors['primary_light']),
                           ('pressed', self.colors['primary_light'])],
                 foreground=[('active', self.colors['primary'])])

        style.map('Medium.TButton',
                 background=[('active', self.colors['primary_light']),
                           ('pressed', self.colors['primary_light'])],
                 foreground=[('active', self.colors['primary'])])

        style.map('Small.TButton',
                 background=[('active', self.colors['primary_light']),
                           ('pressed', self.colors['primary_light'])],
                 foreground=[('active', self.colors['primary'])])

        # 输入框交互效果
        style.map('Large.TEntry',
                 fieldbackground=[('focus', self.colors['surface'])],
                 bordercolor=[('focus', self.colors['primary']),
                            ('!focus', self.colors['divider'])])

        style.map('Medium.TEntry',
                 fieldbackground=[('focus', self.colors['surface'])],
                 bordercolor=[('focus', self.colors['primary']),
                            ('!focus', self.colors['divider'])])

        # 复选框交互效果
        style.map('Medium.TCheckbutton',
                 background=[('active', self.colors['primary_light'])])

        # Notebook标签交互效果
        style.map('Modern.TNotebook.Tab',
                 background=[('selected', self.colors['primary']),
                           ('active', self.colors['primary_light'])],
                 foreground=[('selected', 'white'),
                           ('active', self.colors['primary'])])
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.create_main_frame()
        
        # 创建菜单栏
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主要内容区域
        self.create_content_area()
        
        # 创建状态栏
        self.create_status_bar()
        
    def create_main_frame(self):
        """创建现代化主框架"""
        # 主容器 - 使用现代化背景色
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=12, pady=8)

        # 添加顶部标题栏
        self.create_header_frame()

    def create_header_frame(self):
        """创建现代化标题栏"""
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 12))

        # 应用图标和标题
        title_frame = ttk.Frame(header_frame)
        title_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 主标题
        title_label = ttk.Label(
            title_frame,
            text="🤖 微信自动化添加好友控制台",
            style='Title.TLabel'
        )
        title_label.pack(side=tk.LEFT, anchor=tk.W)

        # 版本标签
        version_label = ttk.Label(
            title_frame,
            text="v1.0.0",
            style='Medium.TLabel'
        )
        version_label.pack(side=tk.LEFT, anchor=tk.W, padx=(10, 0))

        # 右侧状态指示器
        status_frame = ttk.Frame(header_frame)
        status_frame.pack(side=tk.RIGHT)

        self.header_status_indicator = ttk.Label(
            status_frame,
            text="● 系统就绪",
            style='Success.TLabel'
        )
        self.header_status_indicator.pack(side=tk.RIGHT)

        # 添加分割线
        separator = ttk.Separator(self.main_frame, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, pady=(0, 8))
        
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开配置文件", command=self.open_config_file)
        file_menu.add_command(label="打开Excel文件", command=self.open_excel_file)
        file_menu.add_separator()
        file_menu.add_command(label="导出日志", command=self.export_logs)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="配置验证", command=self.validate_configuration)
        tools_menu.add_command(label="清理日志", command=self.clean_logs)
        tools_menu.add_command(label="重置统计", command=self.reset_statistics)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
        
    def create_toolbar(self):
        """创建现代化工具栏"""
        # 工具栏容器
        toolbar_container = ttk.Frame(self.main_frame)
        toolbar_container.pack(fill=tk.X, pady=(0, 12))

        # 主工具栏
        toolbar = ttk.Frame(toolbar_container)
        toolbar.pack(fill=tk.X)

        # 左侧主要控制按钮组
        control_group = ttk.Frame(toolbar)
        control_group.pack(side=tk.LEFT)

        # 开始按钮 - 使用主色调
        self.start_button = ttk.Button(
            control_group,
            text="▶ 开始自动化",
            command=self.start_automation,
            style='Primary.TButton'
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 8))

        # 停止按钮
        self.stop_button = ttk.Button(
            control_group,
            text="⏹ 停止",
            command=self.stop_automation,
            state=tk.DISABLED,
            style='Large.TButton'
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 8))

        # 暂停按钮
        self.pause_button = ttk.Button(
            control_group,
            text="⏸ 暂停",
            command=self.pause_automation,
            state=tk.DISABLED,
            style='Large.TButton'
        )
        self.pause_button.pack(side=tk.LEFT, padx=(0, 16))

        # 分隔线
        separator_frame = ttk.Frame(control_group, width=2, height=30)
        separator_frame.pack(side=tk.LEFT, padx=8)
        separator_frame.configure(style='Modern.TFrame')

        # 功能按钮组
        function_group = ttk.Frame(toolbar)
        function_group.pack(side=tk.LEFT, padx=(8, 0))

        # 统计按钮
        stats_button = ttk.Button(
            function_group,
            text="📊 统计报告",
            command=self.show_statistics_dialog,
            style='Secondary.TButton'
        )
        stats_button.pack(side=tk.LEFT, padx=(0, 8))

        # 配置按钮
        config_button = ttk.Button(
            function_group,
            text="⚙ 配置",
            command=self.open_config_dialog,
            style='Secondary.TButton'
        )
        config_button.pack(side=tk.LEFT, padx=(0, 8))

        # 帮助按钮
        help_button = ttk.Button(
            function_group,
            text="❓ 帮助",
            command=self.show_help,
            style='Secondary.TButton'
        )
        help_button.pack(side=tk.LEFT)

        # 右侧状态区域
        status_group = ttk.Frame(toolbar)
        status_group.pack(side=tk.RIGHT)

        # 状态指示器
        self.status_indicator = ttk.Label(
            status_group,
            text="● 系统就绪",
            style='Success.TLabel'
        )
        self.status_indicator.pack(side=tk.RIGHT)

        # 添加工具栏底部分割线
        toolbar_separator = ttk.Separator(toolbar_container, orient=tk.HORIZONTAL)
        toolbar_separator.pack(fill=tk.X, pady=(8, 0))
        
    def create_content_area(self):
        """创建现代化主要内容区域"""
        # 内容容器
        content_container = ttk.Frame(self.main_frame)
        content_container.pack(fill=tk.BOTH, expand=True)

        # 创建现代化笔记本控件（标签页）
        self.notebook = ttk.Notebook(content_container, style='Modern.TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=4, pady=4)

        # 创建各个现代化标签页
        self.create_control_tab()
        self.create_progress_tab()
        self.create_logs_tab()
        self.create_config_tab()

        # 绑定标签页切换事件
        self.notebook.bind('<<NotebookTabChanged>>', self.on_tab_changed)

    def on_tab_changed(self, event=None):
        """标签页切换事件处理"""
        try:
            selected_tab = self.notebook.index(self.notebook.select())
            tab_names = ["控制面板", "进度监控", "日志", "配置"]
            if selected_tab < len(tab_names):
                self.log_message("INFO", f"切换到 {tab_names[selected_tab]} 标签页")
        except Exception:
            pass
        
    def create_control_tab(self):
        """创建现代化控制标签页"""
        control_frame = ttk.Frame(self.notebook)
        self.notebook.add(control_frame, text="🎮 控制面板")

        # 添加内边距
        control_container = ttk.Frame(control_frame)
        control_container.pack(fill=tk.BOTH, expand=True, padx=16, pady=12)

        # 创建现代化的两列布局
        left_column = ttk.Frame(control_container)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 12))

        right_column = ttk.Frame(control_container)
        right_column.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(12, 0))

        # 左列：执行控制 + 运行参数配置
        self.create_execution_control_section(left_column)
        self.create_runtime_params_section(left_column)

        # 右列：实时状态 + 运行状态监控
        self.create_current_status_section(right_column)
        self.create_status_monitor_section(right_column)

    def create_execution_control_section(self, parent):
        """创建现代化执行控制区域"""
        # 使用现代化LabelFrame样式
        control_frame = ttk.LabelFrame(
            parent,
            text="📋 执行控制",
            padding=20,
            style='Modern.TLabelframe'
        )
        control_frame.pack(fill=tk.X, pady=(0, 16))

        # 文件选择卡片
        file_card = ttk.Frame(control_frame)
        file_card.pack(fill=tk.X, pady=(0, 16))

        # 文件选择标题
        file_title = ttk.Label(
            file_card,
            text="📁 数据文件选择",
            style='Subtitle.TLabel'
        )
        file_title.pack(anchor=tk.W, pady=(0, 8))

        # Excel文件选择
        excel_frame = ttk.Frame(file_card)
        excel_frame.pack(fill=tk.X)

        ttk.Label(
            excel_frame,
            text="Excel文件路径:",
            style='Large.TLabel'
        ).pack(anchor=tk.W, pady=(0, 6))

        excel_input_frame = ttk.Frame(excel_frame)
        excel_input_frame.pack(fill=tk.X)

        self.excel_path_var = tk.StringVar(value="添加好友名单.xlsx")
        self.excel_entry = ttk.Entry(
            excel_input_frame,
            textvariable=self.excel_path_var,
            style='Large.TEntry',
            font=('Segoe UI', 11)
        )
        self.excel_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 12))

        browse_button = ttk.Button(
            excel_input_frame,
            text="📂 浏览文件",
            command=self.browse_excel_file,
            style='Secondary.TButton'
        )
        browse_button.pack(side=tk.RIGHT)

        # 分割线
        separator1 = ttk.Separator(control_frame, orient=tk.HORIZONTAL)
        separator1.pack(fill=tk.X, pady=12)

        # 基础参数卡片
        params_card = ttk.Frame(control_frame)
        params_card.pack(fill=tk.X)

        # 参数标题
        params_title = ttk.Label(
            params_card,
            text="⚙ 基础执行参数",
            style='Subtitle.TLabel'
        )
        params_title.pack(anchor=tk.W, pady=(0, 12))

        # 参数网格容器
        params_grid = ttk.Frame(params_card)
        params_grid.pack(fill=tk.X)
        params_grid.grid_columnconfigure(0, weight=1)
        params_grid.grid_columnconfigure(1, weight=1)

        # 批次大小参数
        self._create_numeric_param(
            params_grid,
            row=0,
            label="批次大小:",
            var_name="batch_size_var",
            default_value="10",
            min_val=1, max_val=100, step=1,
            description="每批处理的联系人数量"
        )

        # 操作延迟参数
        self._create_float_param(
            params_grid,
            row=1,
            label="操作延迟(秒):",
            var_name="delay_var",
            default_value="2.0",
            min_val=0.1, max_val=10.0, step=0.1,
            description="每次操作之间的延迟时间"
        )

    def _create_numeric_param(self, parent, row, label, var_name, default_value, min_val, max_val, step, description=""):
        """创建数值参数控件"""
        # 标签
        ttk.Label(parent, text=label, style='Medium.TLabel').grid(
            row=row, column=0, sticky=tk.W, pady=6, padx=(0, 12)
        )

        # 控制框架
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=row, column=1, sticky=tk.E, pady=6)

        # 创建变量
        setattr(self, var_name, tk.StringVar(value=default_value))
        var = getattr(self, var_name)

        # 减少按钮
        ttk.Button(
            control_frame, text="−", width=3,
            style='Small.TButton',
            command=lambda: self.adjust_numeric_value(var, -step, min_val, max_val)
        ).pack(side=tk.LEFT)

        # 输入框
        entry = ttk.Entry(
            control_frame, textvariable=var, width=8,
            style='Medium.TEntry',
            justify=tk.CENTER
        )
        entry.pack(side=tk.LEFT, padx=4)

        # 增加按钮
        ttk.Button(
            control_frame, text="＋", width=3,
            style='Small.TButton',
            command=lambda: self.adjust_numeric_value(var, step, min_val, max_val)
        ).pack(side=tk.LEFT)

    def _create_float_param(self, parent, row, label, var_name, default_value, min_val, max_val, step, description=""):
        """创建浮点数参数控件"""
        # 标签
        ttk.Label(parent, text=label, style='Medium.TLabel').grid(
            row=row, column=0, sticky=tk.W, pady=6, padx=(0, 12)
        )

        # 控制框架
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=row, column=1, sticky=tk.E, pady=6)

        # 创建变量
        setattr(self, var_name, tk.StringVar(value=default_value))
        var = getattr(self, var_name)

        # 减少按钮
        ttk.Button(
            control_frame, text="−", width=3,
            style='Small.TButton',
            command=lambda: self.adjust_float_value(var, -step, min_val, max_val)
        ).pack(side=tk.LEFT)

        # 输入框
        entry = ttk.Entry(
            control_frame, textvariable=var, width=8,
            style='Medium.TEntry',
            justify=tk.CENTER
        )
        entry.pack(side=tk.LEFT, padx=4)

        # 增加按钮
        ttk.Button(
            control_frame, text="＋", width=3,
            style='Small.TButton',
            command=lambda: self.adjust_float_value(var, step, min_val, max_val)
        ).pack(side=tk.LEFT)

    def create_current_status_section(self, parent):
        """创建现代化当前状态区域"""
        status_frame = ttk.LabelFrame(
            parent,
            text="📊 实时状态",
            padding=20,
            style='Modern.TLabelframe'
        )
        status_frame.pack(fill=tk.X, pady=(0, 16))

        # 状态卡片容器
        status_container = ttk.Frame(status_frame)
        status_container.pack(fill=tk.X)

        # 主状态显示
        main_status_frame = ttk.Frame(status_container)
        main_status_frame.pack(fill=tk.X, pady=(0, 12))

        self.current_status_label = ttk.Label(
            main_status_frame,
            text="🟢 系统就绪",
            style='Title.TLabel'
        )
        self.current_status_label.pack(anchor=tk.W)

        # 分割线
        separator = ttk.Separator(status_container, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, pady=8)

        # 详细状态信息
        details_frame = ttk.Frame(status_container)
        details_frame.pack(fill=tk.X)

        # 执行步骤
        step_frame = ttk.Frame(details_frame)
        step_frame.pack(fill=tk.X, pady=4)

        ttk.Label(
            step_frame,
            text="执行步骤:",
            style='Medium.TLabel'
        ).pack(side=tk.LEFT)

        self.current_step_label = ttk.Label(
            step_frame,
            text="待启动",
            style='Large.TLabel'
        )
        self.current_step_label.pack(side=tk.RIGHT)

        # 当前窗口
        window_frame = ttk.Frame(details_frame)
        window_frame.pack(fill=tk.X, pady=4)

        ttk.Label(
            window_frame,
            text="当前窗口:",
            style='Medium.TLabel'
        ).pack(side=tk.LEFT)

        self.current_window_label = ttk.Label(
            window_frame,
            text="无",
            style='Large.TLabel'
        )
        self.current_window_label.pack(side=tk.RIGHT)

        # 运行时间
        time_frame = ttk.Frame(details_frame)
        time_frame.pack(fill=tk.X, pady=4)

        ttk.Label(
            time_frame,
            text="运行时间:",
            style='Medium.TLabel'
        ).pack(side=tk.LEFT)

        self.runtime_label = ttk.Label(
            time_frame,
            text="00:00:00",
            style='Large.TLabel'
        )
        self.runtime_label.pack(side=tk.RIGHT)

    def create_runtime_params_section(self, parent):
        """创建运行参数配置区域"""
        params_frame = ttk.LabelFrame(parent, text="⚙️ 运行参数配置", padding=12)
        params_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 0))

        # 使用网格布局替代滚动框架，充分利用水平空间
        main_grid = ttk.Frame(params_frame)
        main_grid.pack(fill=tk.BOTH, expand=True)

        # 配置网格列权重，创建两列布局
        main_grid.grid_columnconfigure(0, weight=1)
        main_grid.grid_columnconfigure(1, weight=1)

        # 左列：单次添加间隔配置
        interval_frame = ttk.LabelFrame(main_grid, text="单次添加间隔", padding=8)
        interval_frame.grid(row=0, column=0, sticky="ew", padx=(0, 6), pady=(0, 8))

        # 配置间隔框架的网格
        interval_frame.grid_columnconfigure(1, weight=1)

        # 最小值
        ttk.Label(interval_frame, text="最小值(秒):", style='Medium.TLabel').grid(row=0, column=0, sticky=tk.W, pady=2)
        min_control_frame = ttk.Frame(interval_frame)
        min_control_frame.grid(row=0, column=1, sticky=tk.E, pady=2)

        ttk.Button(min_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["interval_min"], -1, 1, 300)).pack(side=tk.LEFT)
        interval_min_entry = ttk.Entry(min_control_frame, textvariable=self.runtime_params["interval_min"], width=6, style='Medium.TEntry')
        interval_min_entry.pack(side=tk.LEFT, padx=2)
        interval_min_entry.bind('<KeyRelease>', self.on_param_change)
        ttk.Button(min_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["interval_min"], 1, 1, 300)).pack(side=tk.LEFT)

        # 最大值
        ttk.Label(interval_frame, text="最大值(秒):", style='Medium.TLabel').grid(row=1, column=0, sticky=tk.W, pady=2)
        max_control_frame = ttk.Frame(interval_frame)
        max_control_frame.grid(row=1, column=1, sticky=tk.E, pady=2)

        ttk.Button(max_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["interval_max"], -1, 1, 300)).pack(side=tk.LEFT)
        interval_max_entry = ttk.Entry(max_control_frame, textvariable=self.runtime_params["interval_max"], width=6, style='Medium.TEntry')
        interval_max_entry.pack(side=tk.LEFT, padx=2)
        interval_max_entry.bind('<KeyRelease>', self.on_param_change)
        ttk.Button(max_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["interval_max"], 1, 1, 300)).pack(side=tk.LEFT)

        # 右列：数量限制配置
        limits_frame = ttk.LabelFrame(main_grid, text="数量限制", padding=8)
        limits_frame.grid(row=0, column=1, sticky="ew", padx=(6, 0), pady=(0, 8))

        # 配置限制框架的网格
        limits_frame.grid_columnconfigure(1, weight=1)

        # 每日添加上限
        ttk.Label(limits_frame, text="每日添加上限:", style='Medium.TLabel').grid(row=0, column=0, sticky=tk.W, pady=2)
        daily_control_frame = ttk.Frame(limits_frame)
        daily_control_frame.grid(row=0, column=1, sticky=tk.E, pady=2)

        ttk.Button(daily_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["daily_limit"], -10, 10, 1000)).pack(side=tk.LEFT)
        daily_entry = ttk.Entry(daily_control_frame, textvariable=self.runtime_params["daily_limit"], width=6, style='Medium.TEntry')
        daily_entry.pack(side=tk.LEFT, padx=2)
        daily_entry.bind('<KeyRelease>', self.on_param_change)
        ttk.Button(daily_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["daily_limit"], 10, 10, 1000)).pack(side=tk.LEFT)

        # 每窗口最大添加数量
        ttk.Label(limits_frame, text="每窗口最大添加:", style='Medium.TLabel').grid(row=1, column=0, sticky=tk.W, pady=2)
        window_control_frame = ttk.Frame(limits_frame)
        window_control_frame.grid(row=1, column=1, sticky=tk.E, pady=2)

        ttk.Button(window_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["max_per_window"], -1, 0, 100)).pack(side=tk.LEFT)
        window_entry = ttk.Entry(window_control_frame, textvariable=self.runtime_params["max_per_window"], width=6, style='Medium.TEntry')
        window_entry.pack(side=tk.LEFT, padx=2)
        window_entry.bind('<KeyRelease>', self.on_param_change)
        ttk.Button(window_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["max_per_window"], 1, 0, 100)).pack(side=tk.LEFT)

        # 左列：上午时段配置
        morning_frame = ttk.LabelFrame(main_grid, text="上午时段", padding=8)
        morning_frame.grid(row=1, column=0, sticky="ew", padx=(0, 6), pady=(8, 8))

        # 配置上午时段框架的网格
        morning_frame.grid_columnconfigure(1, weight=1)

        # 启用/禁用复选框
        morning_enabled_cb = ttk.Checkbutton(morning_frame, text="启用上午时段",
                                           variable=self.time_slot_enabled["morning_enabled"],
                                           command=self.on_morning_enabled_change,
                                           style='Medium.TCheckbutton')
        morning_enabled_cb.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 8))

        # 开始时间
        ttk.Label(morning_frame, text="开始时间:", style='Medium.TLabel').grid(row=1, column=0, sticky=tk.W, pady=2)
        morning_start_control_frame = ttk.Frame(morning_frame)
        morning_start_control_frame.grid(row=1, column=1, sticky=tk.E, pady=2)

        self.morning_start_minus_btn = ttk.Button(morning_start_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["morning_start"], -15))
        self.morning_start_minus_btn.pack(side=tk.LEFT)
        self.morning_start_entry = ttk.Entry(morning_start_control_frame, textvariable=self.runtime_params["morning_start"], width=6, style='Medium.TEntry')
        self.morning_start_entry.pack(side=tk.LEFT, padx=2)
        self.morning_start_entry.bind('<KeyRelease>', self.on_param_change)
        self.morning_start_plus_btn = ttk.Button(morning_start_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["morning_start"], 15))
        self.morning_start_plus_btn.pack(side=tk.LEFT)

        # 结束时间
        ttk.Label(morning_frame, text="结束时间:", style='Medium.TLabel').grid(row=2, column=0, sticky=tk.W, pady=2)
        morning_end_control_frame = ttk.Frame(morning_frame)
        morning_end_control_frame.grid(row=2, column=1, sticky=tk.E, pady=2)

        self.morning_end_minus_btn = ttk.Button(morning_end_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["morning_end"], -15))
        self.morning_end_minus_btn.pack(side=tk.LEFT)
        self.morning_end_entry = ttk.Entry(morning_end_control_frame, textvariable=self.runtime_params["morning_end"], width=6, style='Medium.TEntry')
        self.morning_end_entry.pack(side=tk.LEFT, padx=2)
        self.morning_end_entry.bind('<KeyRelease>', self.on_param_change)
        self.morning_end_plus_btn = ttk.Button(morning_end_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["morning_end"], 15))
        self.morning_end_plus_btn.pack(side=tk.LEFT)

        # 右列：下午时段配置
        afternoon_frame = ttk.LabelFrame(main_grid, text="下午时段", padding=8)
        afternoon_frame.grid(row=1, column=1, sticky="ew", padx=(6, 0), pady=(8, 8))

        # 配置下午时段框架的网格
        afternoon_frame.grid_columnconfigure(1, weight=1)

        # 启用/禁用复选框
        afternoon_enabled_cb = ttk.Checkbutton(afternoon_frame, text="启用下午时段",
                                             variable=self.time_slot_enabled["afternoon_enabled"],
                                             command=self.on_afternoon_enabled_change,
                                             style='Medium.TCheckbutton')
        afternoon_enabled_cb.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 8))

        # 开始时间
        ttk.Label(afternoon_frame, text="开始时间:", style='Medium.TLabel').grid(row=1, column=0, sticky=tk.W, pady=2)
        afternoon_start_control_frame = ttk.Frame(afternoon_frame)
        afternoon_start_control_frame.grid(row=1, column=1, sticky=tk.E, pady=2)

        self.afternoon_start_minus_btn = ttk.Button(afternoon_start_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["afternoon_start"], -15))
        self.afternoon_start_minus_btn.pack(side=tk.LEFT)
        self.afternoon_start_entry = ttk.Entry(afternoon_start_control_frame, textvariable=self.runtime_params["afternoon_start"], width=6, style='Medium.TEntry')
        self.afternoon_start_entry.pack(side=tk.LEFT, padx=2)
        self.afternoon_start_entry.bind('<KeyRelease>', self.on_param_change)
        self.afternoon_start_plus_btn = ttk.Button(afternoon_start_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["afternoon_start"], 15))
        self.afternoon_start_plus_btn.pack(side=tk.LEFT)

        # 结束时间
        ttk.Label(afternoon_frame, text="结束时间:", style='Medium.TLabel').grid(row=2, column=0, sticky=tk.W, pady=2)
        afternoon_end_control_frame = ttk.Frame(afternoon_frame)
        afternoon_end_control_frame.grid(row=2, column=1, sticky=tk.E, pady=2)

        self.afternoon_end_minus_btn = ttk.Button(afternoon_end_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["afternoon_end"], -15))
        self.afternoon_end_minus_btn.pack(side=tk.LEFT)
        self.afternoon_end_entry = ttk.Entry(afternoon_end_control_frame, textvariable=self.runtime_params["afternoon_end"], width=6, style='Medium.TEntry')
        self.afternoon_end_entry.pack(side=tk.LEFT, padx=2)
        self.afternoon_end_entry.bind('<KeyRelease>', self.on_param_change)
        self.afternoon_end_plus_btn = ttk.Button(afternoon_end_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["afternoon_end"], 15))
        self.afternoon_end_plus_btn.pack(side=tk.LEFT)

        # 左列：休息触发条件配置
        trigger_frame = ttk.LabelFrame(main_grid, text="休息触发条件", padding=8)
        trigger_frame.grid(row=2, column=0, sticky="ew", padx=(0, 6), pady=(8, 8))

        # 配置触发条件框架的网格
        trigger_frame.grid_columnconfigure(1, weight=1)

        # 触发数量设置
        ttk.Label(trigger_frame, text="每添加好友数:", style='Medium.TLabel').grid(row=0, column=0, sticky=tk.W, pady=2)
        trigger_control_frame = ttk.Frame(trigger_frame)
        trigger_control_frame.grid(row=0, column=1, sticky=tk.E, pady=2)

        ttk.Button(trigger_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["rest_trigger"], -1, 1, 100)).pack(side=tk.LEFT)
        trigger_entry = ttk.Entry(trigger_control_frame, textvariable=self.runtime_params["rest_trigger"], width=6, style='Medium.TEntry')
        trigger_entry.pack(side=tk.LEFT, padx=2)
        trigger_entry.bind('<KeyRelease>', self.on_param_change)
        ttk.Button(trigger_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["rest_trigger"], 1, 1, 100)).pack(side=tk.LEFT)

        # 说明文字
        ttk.Label(trigger_frame, text="个好友后自动休息", style='Medium.TLabel').grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # 右列：休息时长设置配置
        duration_frame = ttk.LabelFrame(main_grid, text="休息时长设置", padding=8)
        duration_frame.grid(row=2, column=1, sticky="ew", padx=(6, 0), pady=(8, 8))

        # 配置休息时长框架的网格
        duration_frame.grid_columnconfigure(1, weight=1)

        # 休息时长设置
        ttk.Label(duration_frame, text="休息时长(分钟):", style='Medium.TLabel').grid(row=0, column=0, sticky=tk.W, pady=2)
        duration_control_frame = ttk.Frame(duration_frame)
        duration_control_frame.grid(row=0, column=1, sticky=tk.E, pady=2)

        ttk.Button(duration_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["rest_duration"], -1, 1, 60)).pack(side=tk.LEFT)
        duration_entry = ttk.Entry(duration_control_frame, textvariable=self.runtime_params["rest_duration"], width=6, style='Medium.TEntry')
        duration_entry.pack(side=tk.LEFT, padx=2)
        duration_entry.bind('<KeyRelease>', self.on_param_change)
        ttk.Button(duration_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["rest_duration"], 1, 1, 60)).pack(side=tk.LEFT)

        # 说明文字
        ttk.Label(duration_frame, text="分钟后继续执行", style='Medium.TLabel').grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # 参数操作按钮 - 跨两列显示
        button_frame = ttk.Frame(main_grid)
        button_frame.grid(row=3, column=0, columnspan=2, sticky="ew", pady=(12, 0))

        ttk.Button(button_frame, text="验证", command=self.validate_runtime_params, width=10, style='Medium.TButton').pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(button_frame, text="保存", command=self.save_runtime_params, width=10, style='Medium.TButton').pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(button_frame, text="重置", command=self.reset_runtime_params, width=10, style='Medium.TButton').pack(side=tk.LEFT)

        # 初始化时段启用状态
        self.on_morning_enabled_change()
        self.on_afternoon_enabled_change()

    def create_status_monitor_section(self, parent):
        """创建运行状态监控区域"""
        status_frame = ttk.LabelFrame(parent, text="📊 运行状态监控", padding=15)
        status_frame.pack(fill=tk.BOTH, expand=True)

        # 总体进度显示
        progress_frame = ttk.LabelFrame(status_frame, text="总体进度", padding=10)
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        # 进度条和百分比
        progress_info_frame = ttk.Frame(progress_frame)
        progress_info_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(progress_info_frame, text="总进度:", style='Large.TLabel').pack(side=tk.LEFT)
        self.total_progress_label = ttk.Label(progress_info_frame, textvariable=self.status_vars["total_progress"],
                                            font=('Arial', 12, 'bold'), foreground='#3498db')
        self.total_progress_label.pack(side=tk.RIGHT)

        # 进度条
        self.main_progress_bar = ttk.Progressbar(progress_frame, mode='determinate', length=350)
        self.main_progress_bar.pack(fill=tk.X)

        # 详细统计信息
        stats_frame = ttk.LabelFrame(status_frame, text="详细统计", padding=10)
        stats_frame.pack(fill=tk.BOTH, expand=True)

        # 创建两列布局
        left_stats = ttk.Frame(stats_frame)
        left_stats.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        right_stats = ttk.Frame(stats_frame)
        right_stats.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # 左列统计
        self.create_status_item(left_stats, "计划处理:", self.status_vars["planned_count"])
        self.create_status_item(left_stats, "当前进度:", self.status_vars["current_progress"])
        self.create_status_item(left_stats, "成功添加:", self.status_vars["success_count"], '#27ae60')

        # 右列统计
        self.create_status_item(right_stats, "失败/错误:", self.status_vars["error_count"], '#e74c3c')
        self.create_status_item(right_stats, "当前微信:", self.status_vars["current_window"])
        self.create_status_item(right_stats, "操作倒计时:", self.status_vars["countdown"], '#f39c12')

        # 实时状态更新
        self.update_status_display()

    def create_status_item(self, parent, label_text, text_var, color='#2c3e50'):
        """创建状态显示项"""
        frame = ttk.Frame(parent)
        frame.pack(fill=tk.X, pady=3)

        ttk.Label(frame, text=label_text, font=('Arial', 11), foreground='#34495e').pack(side=tk.LEFT)
        status_label = ttk.Label(frame, textvariable=text_var, font=('Arial', 11, 'bold'))
        status_label.configure(foreground=color)
        status_label.pack(side=tk.RIGHT)

    def create_progress_tab(self):
        """创建现代化进度监控标签页"""
        progress_frame = ttk.Frame(self.notebook)
        self.notebook.add(progress_frame, text="📈 进度监控")

        # 添加内边距
        progress_container = ttk.Frame(progress_frame)
        progress_container.pack(fill=tk.BOTH, expand=True, padx=16, pady=12)

        # 总体进度卡片
        overall_frame = ttk.LabelFrame(
            progress_container,
            text="📊 总体执行进度",
            padding=20,
            style='Modern.TLabelframe'
        )
        overall_frame.pack(fill=tk.X, pady=(0, 16))

        # 联系人处理进度
        contact_section = ttk.Frame(overall_frame)
        contact_section.pack(fill=tk.X, pady=(0, 16))

        # 联系人进度标题和数值
        contact_header = ttk.Frame(contact_section)
        contact_header.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(
            contact_header,
            text="👥 联系人处理进度",
            style='Subtitle.TLabel'
        ).pack(side=tk.LEFT)

        self.contact_progress_label = ttk.Label(
            contact_header,
            text="0 / 0 (0%)",
            style='Info.TLabel'
        )
        self.contact_progress_label.pack(side=tk.RIGHT)

        # 联系人进度条
        self.contact_progress = ttk.Progressbar(
            contact_section,
            mode='determinate',
            style='Modern.Horizontal.TProgressbar',
            length=600
        )
        self.contact_progress.pack(fill=tk.X)

        # 分割线
        separator1 = ttk.Separator(overall_frame, orient=tk.HORIZONTAL)
        separator1.pack(fill=tk.X, pady=12)

        # 窗口处理进度
        window_section = ttk.Frame(overall_frame)
        window_section.pack(fill=tk.X)

        # 窗口进度标题和数值
        window_header = ttk.Frame(window_section)
        window_header.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(
            window_header,
            text="🪟 窗口处理进度",
            style='Subtitle.TLabel'
        ).pack(side=tk.LEFT)

        self.window_progress_label = ttk.Label(
            window_header,
            text="0 / 0 (0%)",
            style='Info.TLabel'
        )
        self.window_progress_label.pack(side=tk.RIGHT)

        # 窗口进度条
        self.window_progress = ttk.Progressbar(
            window_section,
            mode='determinate',
            style='Modern.Horizontal.TProgressbar',
            length=600
        )
        self.window_progress.pack(fill=tk.X)

        # 详细统计
        details_frame = ttk.LabelFrame(progress_frame, text="详细统计", padding=15)
        details_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建统计表格
        columns = ("项目", "数量", "百分比", "状态")
        self.stats_tree = ttk.Treeview(details_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.stats_tree.heading(col, text=col)
            self.stats_tree.column(col, width=120)

        # 配置表格字体
        style = ttk.Style()
        style.configure("Treeview", font=('Arial', 11))
        style.configure("Treeview.Heading", font=('Arial', 12, 'bold'))

        # 滚动条
        stats_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.stats_tree.yview)
        self.stats_tree.configure(yscrollcommand=stats_scrollbar.set)

        self.stats_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 初始化统计数据
        self.update_statistics_display()

    def create_logs_tab(self):
        """创建现代化日志标签页"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="📝 日志")

        # 添加内边距
        logs_container = ttk.Frame(logs_frame)
        logs_container.pack(fill=tk.BOTH, expand=True, padx=16, pady=12)

        # 日志控制面板
        control_panel = ttk.LabelFrame(
            logs_container,
            text="🎛 日志控制",
            padding=16,
            style='Modern.TLabelframe'
        )
        control_panel.pack(fill=tk.X, pady=(0, 12))

        # 控制按钮行
        control_row1 = ttk.Frame(control_panel)
        control_row1.pack(fill=tk.X, pady=(0, 8))

        # 日志级别选择
        level_frame = ttk.Frame(control_row1)
        level_frame.pack(side=tk.LEFT)

        ttk.Label(
            level_frame,
            text="日志级别:",
            style='Medium.TLabel'
        ).pack(side=tk.LEFT, padx=(0, 8))

        self.log_level_var = tk.StringVar(value="INFO")
        log_level_combo = ttk.Combobox(
            level_frame,
            textvariable=self.log_level_var,
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            state="readonly",
            width=12,
            font=('Segoe UI', 11)
        )
        log_level_combo.pack(side=tk.LEFT)

        # 右侧选项
        options_frame = ttk.Frame(control_row1)
        options_frame.pack(side=tk.RIGHT)

        # 自动滚动选项
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_cb = ttk.Checkbutton(
            options_frame,
            text="自动滚动",
            variable=self.auto_scroll_var,
            style='Medium.TCheckbutton'
        )
        auto_scroll_cb.pack(side=tk.RIGHT, padx=(16, 0))

        # 控制按钮行
        control_row2 = ttk.Frame(control_panel)
        control_row2.pack(fill=tk.X)

        # 左侧按钮组
        button_group = ttk.Frame(control_row2)
        button_group.pack(side=tk.LEFT)

        ttk.Button(
            button_group,
            text="🗑 清空日志",
            command=self.clear_logs,
            style='Secondary.TButton'
        ).pack(side=tk.LEFT, padx=(0, 8))

        ttk.Button(
            button_group,
            text="💾 保存日志",
            command=self.save_logs,
            style='Secondary.TButton'
        ).pack(side=tk.LEFT, padx=(0, 8))

        ttk.Button(
            button_group,
            text="📤 导出日志",
            command=self.export_logs,
            style='Secondary.TButton'
        ).pack(side=tk.LEFT)

        # 日志显示区域
        log_display_frame = ttk.LabelFrame(
            logs_container,
            text="📋 日志输出",
            padding=12,
            style='Modern.TLabelframe'
        )
        log_display_frame.pack(fill=tk.BOTH, expand=True)

        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(
            log_display_frame,
            wrap=tk.WORD,
            height=20,
            font=('Consolas', 11),
            bg=self.colors['surface'],
            fg=self.colors['on_surface'],
            selectbackground=self.colors['primary_light'],
            insertbackground=self.colors['primary'],
            borderwidth=1,
            relief='solid'
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 配置现代化日志颜色
        self.log_text.tag_configure("INFO",
                                   foreground=self.colors['on_surface'])
        self.log_text.tag_configure("WARNING",
                                   foreground=self.colors['warning'],
                                   font=('Consolas', 11, 'bold'))
        self.log_text.tag_configure("ERROR",
                                   foreground=self.colors['error'],
                                   font=('Consolas', 11, 'bold'))
        self.log_text.tag_configure("DEBUG",
                                   foreground=self.colors['on_background'])
        self.log_text.tag_configure("SUCCESS",
                                   foreground=self.colors['success'],
                                   font=('Consolas', 11, 'bold'))

    def create_config_tab(self):
        """创建现代化配置标签页"""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="⚙ 配置")

        # 添加内边距
        config_container = ttk.Frame(config_frame)
        config_container.pack(fill=tk.BOTH, expand=True, padx=16, pady=12)

        # 配置控制面板
        control_panel = ttk.LabelFrame(
            config_container,
            text="🛠 配置管理",
            padding=16,
            style='Modern.TLabelframe'
        )
        control_panel.pack(fill=tk.X, pady=(0, 12))

        # 控制按钮行
        button_row = ttk.Frame(control_panel)
        button_row.pack(fill=tk.X)

        # 左侧按钮组
        left_buttons = ttk.Frame(button_row)
        left_buttons.pack(side=tk.LEFT)

        ttk.Button(
            left_buttons,
            text="📂 加载配置",
            command=self.load_configuration,
            style='Primary.TButton'
        ).pack(side=tk.LEFT, padx=(0, 8))

        ttk.Button(
            left_buttons,
            text="💾 保存配置",
            command=self.save_configuration,
            style='Secondary.TButton'
        ).pack(side=tk.LEFT, padx=(0, 8))

        ttk.Button(
            left_buttons,
            text="🔄 重置默认",
            command=self.reset_configuration,
            style='Secondary.TButton'
        ).pack(side=tk.LEFT, padx=(0, 8))

        # 右侧验证按钮
        right_buttons = ttk.Frame(button_row)
        right_buttons.pack(side=tk.RIGHT)

        ttk.Button(
            right_buttons,
            text="✅ 验证配置",
            command=self.validate_configuration,
            style='Secondary.TButton'
        ).pack(side=tk.RIGHT)

        # 配置编辑器区域
        editor_frame = ttk.LabelFrame(
            config_container,
            text="📝 配置编辑器",
            padding=12,
            style='Modern.TLabelframe'
        )
        editor_frame.pack(fill=tk.BOTH, expand=True)

        # 配置文本编辑器
        self.config_text = scrolledtext.ScrolledText(
            editor_frame,
            wrap=tk.WORD,
            height=24,
            font=('Consolas', 11),
            bg=self.colors['surface'],
            fg=self.colors['on_surface'],
            selectbackground=self.colors['primary_light'],
            insertbackground=self.colors['primary'],
            borderwidth=1,
            relief='solid'
        )
        self.config_text.pack(fill=tk.BOTH, expand=True)

        # 添加语法高亮（简单的JSON高亮）
        self._setup_json_syntax_highlighting()



    def _setup_json_syntax_highlighting(self):
        """设置JSON语法高亮"""
        # 配置JSON语法高亮标签
        self.config_text.tag_configure("json_key",
                                      foreground=self.colors['primary'],
                                      font=('Consolas', 11, 'bold'))
        self.config_text.tag_configure("json_string",
                                      foreground=self.colors['success'])
        self.config_text.tag_configure("json_number",
                                      foreground=self.colors['warning'])
        self.config_text.tag_configure("json_boolean",
                                      foreground=self.colors['info'])
        self.config_text.tag_configure("json_null",
                                      foreground=self.colors['error'])

    def create_status_bar(self):
        """创建现代化状态栏"""
        # 状态栏容器
        status_container = ttk.Frame(self.root)
        status_container.pack(fill=tk.X, side=tk.BOTTOM)

        # 状态栏主体
        self.status_bar = ttk.Frame(status_container)
        self.status_bar.pack(fill=tk.X, padx=8, pady=4)

        # 左侧状态信息
        status_left = ttk.Frame(self.status_bar)
        status_left.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 状态图标和文本
        self.status_icon = ttk.Label(
            status_left,
            text="🟢",
            style='Medium.TLabel'
        )
        self.status_icon.pack(side=tk.LEFT, padx=(0, 6))

        self.status_text = ttk.Label(
            status_left,
            text="系统就绪",
            style='Medium.TLabel'
        )
        self.status_text.pack(side=tk.LEFT)

        # 右侧信息区域
        status_right = ttk.Frame(self.status_bar)
        status_right.pack(side=tk.RIGHT)

        # 版本信息
        version_label = ttk.Label(
            status_right,
            text="v1.0.0",
            style='Small.TLabel'
        )
        version_label.pack(side=tk.RIGHT, padx=(12, 0))

        # 分隔符
        ttk.Label(
            status_right,
            text="│",
            style='Small.TLabel'
        ).pack(side=tk.RIGHT, padx=6)

        # 时间显示
        self.time_label = ttk.Label(
            status_right,
            text="",
            style='Small.TLabel'
        )
        self.time_label.pack(side=tk.RIGHT)

        # 添加顶部分割线
        top_separator = ttk.Separator(status_container, orient=tk.HORIZONTAL)
        top_separator.pack(fill=tk.X, side=tk.TOP)

        # 更新时间
        self.update_time()

    def setup_logging(self):
        """设置日志处理"""
        # 创建自定义日志处理器
        self.log_handler = GUILogHandler(self.message_queue)
        self.log_handler.setLevel(logging.INFO)

        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.log_handler.setFormatter(formatter)

        # 添加到根日志记录器
        logging.getLogger().addHandler(self.log_handler)

    # ==================== 核心功能方法 ====================

    def start_automation(self):
        """开始自动化流程"""
        if self.is_running:
            messagebox.showwarning("警告", "自动化流程已在运行中")
            return

        try:
            # 验证配置
            if not self.validate_before_start():
                return

            # 更新现代化界面状态
            self.is_running = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.NORMAL)

            # 更新所有状态指示器
            self.status_indicator.config(text="● 运行中", style='Warning.TLabel')
            if hasattr(self, 'header_status_indicator'):
                self.header_status_indicator.config(text="● 运行中", style='Warning.TLabel')

            # 更新状态栏
            if hasattr(self, 'status_icon'):
                self.status_icon.config(text="🟡")
            if hasattr(self, 'status_text'):
                self.status_text.config(text="正在运行")

            # 更新主状态标签
            if hasattr(self, 'current_status_label'):
                self.current_status_label.config(text="🟡 正在运行")

            self.update_status("正在启动自动化流程...")

            # 启动自动化线程
            self.automation_thread = threading.Thread(
                target=self.run_automation_thread,
                daemon=True
            )
            self.automation_thread.start()

            self.log_message("INFO", "自动化流程已启动")

        except Exception as e:
            self.log_message("ERROR", f"启动自动化流程失败: {e}")
            self.reset_ui_state()

    def stop_automation(self):
        """停止自动化流程"""
        if not self.is_running:
            return

        try:
            self.is_running = False
            self.update_status("正在停止自动化流程...")

            # 停止控制器
            if self.controller:
                # 这里可以添加控制器的停止方法
                pass

            self.reset_ui_state()
            self.log_message("INFO", "自动化流程已停止")

        except Exception as e:
            self.log_message("ERROR", f"停止自动化流程失败: {e}")

    def pause_automation(self):
        """暂停/恢复自动化流程"""
        # 这里可以实现暂停逻辑
        pass

    def run_automation_thread(self):
        """在后台线程中运行自动化流程"""
        try:
            # 初始化控制器
            if hasattr(self, 'excel_path_var') and self.excel_path_var is not None:
                excel_file = self.excel_path_var.get()
            else:
                excel_file = "添加好友名单.xlsx"  # 默认文件名
            config_file = "config.json"

            self.controller = WeChatMainController(excel_file, config_file)

            # 获取微信窗口
            self.update_status("正在扫描微信窗口...")
            windows = self.controller.get_wechat_windows()

            if not windows:
                self.log_message("ERROR", "未找到微信窗口")
                self.reset_ui_state()
                return

            # 加载联系人数据
            self.update_status("正在加载联系人数据...")
            contacts = self.controller.load_contacts_data()

            if not contacts:
                self.log_message("ERROR", "未找到待处理的联系人")
                self.reset_ui_state()
                return

            # 更新统计信息
            self.execution_stats.update(self.controller.execution_stats)
            self.update_statistics_display()

            # 移动所有窗口到指定位置
            self.update_status("正在移动微信窗口...")
            if not self.controller.move_all_windows_to_target_position(windows):
                self.log_message("WARNING", "窗口移动失败，但继续执行")

            # 执行多窗口流程
            self.update_status("正在执行自动化流程...")
            result = self.controller.execute_multi_window_flow(windows, contacts)

            if result:
                self.log_message("INFO", "自动化流程执行完成")
                self.update_status("自动化流程执行完成")
            else:
                self.log_message("ERROR", "自动化流程执行失败")
                self.update_status("自动化流程执行失败")

        except Exception as e:
            self.log_message("ERROR", f"自动化流程异常: {e}")
            self.update_status(f"自动化流程异常: {e}")
        finally:
            self.reset_ui_state()

    def validate_before_start(self) -> bool:
        """启动前验证"""
        # 检查Excel文件
        if hasattr(self, 'excel_path_var') and self.excel_path_var is not None:
            excel_file = self.excel_path_var.get()
            if not excel_file or not Path(excel_file).exists():
                messagebox.showerror("错误", "请选择有效的Excel文件")
                return False
        else:
            messagebox.showerror("错误", "Excel文件路径未设置")
            return False

        # 检查配置文件
        if not Path("config.json").exists():
            messagebox.showerror("错误", "配置文件config.json不存在")
            return False

        return True

    def reset_ui_state(self):
        """重置现代化界面状态"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.DISABLED)

        # 更新状态指示器
        self.status_indicator.config(text="● 系统就绪", style='Success.TLabel')
        if hasattr(self, 'header_status_indicator'):
            self.header_status_indicator.config(text="● 系统就绪", style='Success.TLabel')

        # 更新状态栏
        if hasattr(self, 'status_icon'):
            self.status_icon.config(text="🟢")
        if hasattr(self, 'status_text'):
            self.status_text.config(text="系统就绪")

        # 更新主状态标签
        if hasattr(self, 'current_status_label'):
            self.current_status_label.config(text="🟢 系统就绪")

    # ==================== 界面更新方法 ====================

    def update_status(self, message: str):
        """更新状态信息"""
        self.message_queue.put(('status', message))

    def log_message(self, level: str, message: str):
        """添加日志消息"""
        self.message_queue.put(('log', level, message))

    def update_statistics_display(self):
        """更新统计显示"""
        # 更新进度条
        total_contacts = self.execution_stats.get("total_contacts", 0)
        processed_contacts = self.execution_stats.get("processed_contacts", 0)

        if total_contacts > 0:
            progress_percent = (processed_contacts / total_contacts) * 100
            self.contact_progress.config(value=progress_percent)
            self.contact_progress_label.config(
                text=f"{processed_contacts} / {total_contacts} ({progress_percent:.1f}%)"
            )

        total_windows = self.execution_stats.get("total_windows", 0)
        completed_windows = self.execution_stats.get("completed_windows", 0)

        if total_windows > 0:
            window_progress_percent = (completed_windows / total_windows) * 100
            self.window_progress.config(value=window_progress_percent)
            self.window_progress_label.config(
                text=f"{completed_windows} / {total_windows} ({window_progress_percent:.1f}%)"
            )

        # 更新详细统计表格
        self.stats_tree.delete(*self.stats_tree.get_children())

        stats_data = [
            ("总联系人", self.execution_stats.get("total_contacts", 0)),
            ("已处理", self.execution_stats.get("processed_contacts", 0)),
            ("成功添加", self.execution_stats.get("successful_adds", 0)),
            ("失败", self.execution_stats.get("failed_adds", 0)),
            ("跳过", self.execution_stats.get("skipped_contacts", 0)),
            ("总窗口", self.execution_stats.get("total_windows", 0)),
            ("完成窗口", self.execution_stats.get("completed_windows", 0))
        ]

        for name, count in stats_data:
            total = self.execution_stats.get("total_contacts", 1)
            if name in ["总窗口", "完成窗口"]:
                total = self.execution_stats.get("total_windows", 1)

            percentage = (count / total * 100) if total > 0 else 0
            status = "完成" if percentage == 100 else "进行中" if percentage > 0 else "待开始"

            self.stats_tree.insert("", tk.END, values=(
                name, count, f"{percentage:.1f}%", status
            ))

    def process_messages(self):
        """处理消息队列"""
        try:
            while True:
                message = self.message_queue.get_nowait()

                if message[0] == 'status':
                    # 更新状态栏文本
                    if hasattr(self, 'status_text'):
                        self.status_text.config(text=message[1])

                    # 更新主状态标签
                    if hasattr(self, 'current_status_label'):
                        status_text = message[1]
                        if "启动" in status_text or "运行" in status_text:
                            icon = "🟡"
                        elif "完成" in status_text or "成功" in status_text:
                            icon = "🟢"
                        elif "失败" in status_text or "错误" in status_text:
                            icon = "🔴"
                        else:
                            icon = "🟢"
                        self.current_status_label.config(text=f"{icon} {status_text}")

                elif message[0] == 'log':
                    level, text = message[1], message[2]
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    log_line = f"[{timestamp}] {level}: {text}\n"

                    self.log_text.insert(tk.END, log_line, level)

                    if self.auto_scroll_var.get():
                        self.log_text.see(tk.END)

        except queue.Empty:
            pass
        finally:
            # 每100ms检查一次消息队列
            self.root.after(100, self.process_messages)

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    # ==================== 文件操作方法 ====================

    def browse_excel_file(self):
        """浏览Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename and hasattr(self, 'excel_path_var') and self.excel_path_var is not None:
            self.excel_path_var.set(filename)

    def open_config_file(self):
        """打开配置文件"""
        try:
            import os
            os.startfile("config.json")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开配置文件: {e}")

    def open_excel_file(self):
        """打开Excel文件"""
        try:
            import os
            if hasattr(self, 'excel_path_var') and self.excel_path_var is not None:
                excel_file = self.excel_path_var.get()
                if excel_file and Path(excel_file).exists():
                    os.startfile(excel_file)
                else:
                    messagebox.showerror("错误", "Excel文件不存在")
            else:
                messagebox.showerror("错误", "Excel文件路径未设置")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开Excel文件: {e}")

    def load_configuration(self):
        """加载配置"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                config_data = f.read()

            self.config_text.delete(1.0, tk.END)
            self.config_text.insert(1.0, config_data)

            # 解析配置并更新界面
            config = json.loads(config_data)
            # 更新基础参数（如果变量存在且已初始化）
            if hasattr(self, 'batch_size_var') and self.batch_size_var is not None:
                self.batch_size_var.set(str(config.get("batch_size", 10)))
            if hasattr(self, 'delay_var') and self.delay_var is not None:
                delay_range = config.get("delay_range", [1.5, 3.0])
                self.delay_var.set(str(delay_range[0]))

            self.log_message("INFO", "配置文件加载成功")

        except Exception as e:
            self.log_message("ERROR", f"加载配置文件失败: {e}")

    def save_configuration(self):
        """保存配置"""
        try:
            config_data = self.config_text.get(1.0, tk.END).strip()

            # 验证JSON格式
            json.loads(config_data)

            with open("config.json", "w", encoding="utf-8") as f:
                f.write(config_data)

            self.log_message("INFO", "配置文件保存成功")
            messagebox.showinfo("成功", "配置文件保存成功")

        except json.JSONDecodeError as e:
            messagebox.showerror("错误", f"配置文件格式错误: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置文件失败: {e}")

    def reset_configuration(self):
        """重置配置为默认值"""
        if messagebox.askyesno("确认", "确定要重置配置为默认值吗？"):
            try:
                # 这里可以加载默认配置
                self.load_configuration()
                self.log_message("INFO", "配置已重置为默认值")
            except Exception as e:
                self.log_message("ERROR", f"重置配置失败: {e}")

    # ==================== 工具方法 ====================

    def validate_configuration(self):
        """验证配置"""
        try:
            # 检查配置文件
            if not Path("config.json").exists():
                messagebox.showerror("错误", "配置文件不存在")
                return

            with open("config.json", "r", encoding="utf-8") as f:
                json.load(f)  # 验证JSON格式

            # 检查Excel文件
            if hasattr(self, 'excel_path_var') and self.excel_path_var is not None:
                excel_file = self.excel_path_var.get()
                if not excel_file or not Path(excel_file).exists():
                    messagebox.showerror("错误", "Excel文件不存在")
                    return
            else:
                messagebox.showerror("错误", "Excel文件路径未设置")
                return

            # 检查必要的模块
            required_modules = ["modules", "modules.window_manager", "modules.data_manager"]
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError as e:
                    messagebox.showerror("错误", f"缺少必要模块: {module}")
                    return

            messagebox.showinfo("成功", "配置验证通过")
            self.log_message("INFO", "配置验证通过")

        except Exception as e:
            messagebox.showerror("错误", f"配置验证失败: {e}")

    def clear_logs(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("INFO", "日志已清空")

    def save_logs(self):
        """保存日志"""
        try:
            filename = filedialog.asksaveasfilename(
                title="保存日志",
                defaultextension=".log",
                filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                log_content = self.log_text.get(1.0, tk.END)
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(log_content)

                messagebox.showinfo("成功", f"日志已保存到: {filename}")
                self.log_message("INFO", f"日志已保存到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {e}")

    def export_logs(self):
        """导出日志"""
        self.save_logs()

    def clean_logs(self):
        """清理日志文件"""
        try:
            logs_dir = Path("logs")
            if logs_dir.exists():
                # 这里可以实现日志清理逻辑
                messagebox.showinfo("信息", "日志清理功能待实现")
            else:
                messagebox.showinfo("信息", "日志目录不存在")
        except Exception as e:
            messagebox.showerror("错误", f"清理日志失败: {e}")

    def reset_statistics(self):
        """重置统计信息"""
        if messagebox.askyesno("确认", "确定要重置统计信息吗？"):
            self.execution_stats = {
                "total_contacts": 0,
                "processed_contacts": 0,
                "successful_adds": 0,
                "failed_adds": 0,
                "skipped_contacts": 0,
                "total_windows": 0,
                "completed_windows": 0,
                "start_time": None,
                "end_time": None
            }
            self.update_statistics_display()
            self.log_message("INFO", "统计信息已重置")

    def open_config_dialog(self):
        """打开配置对话框"""
        # 切换到配置标签页
        self.notebook.select(4)  # 配置标签页的索引

    def show_statistics_dialog(self):
        """显示统计对话框"""
        # 切换到进度监控标签页
        self.notebook.select(1)  # 进度监控标签页的索引

    def show_help(self):
        """显示帮助信息"""
        help_text = """
微信自动化添加好友控制台 v1.0.0

使用说明：
1. 在"控制面板"中选择Excel文件并配置运行参数
2. 在"控制面板"右侧查看实时运行状态
3. 点击"开始自动化"按钮开始执行
4. 在"进度监控"中查看详细执行进度
5. 在"日志"中查看详细执行信息

注意事项：
- 确保微信已登录并可见
- Excel文件格式必须正确
- 建议在执行前备份数据
- 遇到问题请查看日志信息

技术支持：
- 查看README.md获取详细说明
- 检查config.json配置文件
- 查看logs目录下的日志文件
        """

        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("500x400")
        help_window.resizable(False, False)

        text_widget = scrolledtext.ScrolledText(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(1.0, help_text)
        text_widget.config(state=tk.DISABLED)

    def show_about(self):
        """显示关于信息"""
        about_text = """
微信自动化添加好友控制台
版本：v1.0.0
创建时间：2025-01-28

功能特性：
• 6步骤严格执行流程
• 多窗口循环处理
• 智能错误处理
• 实时进度监控
• 可视化操作界面

技术架构：
• Python + tkinter
• 模块化设计
• 多线程处理
• 配置驱动

开发者：AI助手
        """
        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_running:
            if messagebox.askyesno("确认", "自动化流程正在运行，确定要退出吗？"):
                self.stop_automation()
                self.root.after(1000, self.root.destroy)
        else:
            self.root.destroy()

    # ==================== 运行参数配置方法 ====================

    def on_param_change(self, _=None):
        """参数变化时的回调函数"""
        try:
            # 实时验证和应用参数
            self.validate_and_apply_params()
        except Exception as e:
            self.log_message("WARNING", f"参数验证失败: {e}")

    def on_morning_enabled_change(self):
        """上午时段启用状态变化回调"""
        enabled = self.time_slot_enabled["morning_enabled"].get()
        state = "normal" if enabled else "disabled"

        # 更新输入框状态
        self.morning_start_entry.config(state=state)
        self.morning_end_entry.config(state=state)

        # 更新按钮状态
        self.morning_start_minus_btn.config(state=state)
        self.morning_start_plus_btn.config(state=state)
        self.morning_end_minus_btn.config(state=state)
        self.morning_end_plus_btn.config(state=state)

        # 触发参数变化事件
        self.on_param_change()

    def on_afternoon_enabled_change(self):
        """下午时段启用状态变化回调"""
        enabled = self.time_slot_enabled["afternoon_enabled"].get()
        state = "normal" if enabled else "disabled"

        # 更新输入框状态
        self.afternoon_start_entry.config(state=state)
        self.afternoon_end_entry.config(state=state)

        # 更新按钮状态
        self.afternoon_start_minus_btn.config(state=state)
        self.afternoon_start_plus_btn.config(state=state)
        self.afternoon_end_minus_btn.config(state=state)
        self.afternoon_end_plus_btn.config(state=state)

        # 触发参数变化事件
        self.on_param_change()

    def validate_and_apply_params(self):
        """验证并应用参数"""
        try:
            # 验证数值参数
            interval_min = int(self.runtime_params["interval_min"].get())
            interval_max = int(self.runtime_params["interval_max"].get())
            daily_limit = int(self.runtime_params["daily_limit"].get())
            max_per_window = int(self.runtime_params["max_per_window"].get())
            rest_trigger = int(self.runtime_params["rest_trigger"].get())
            rest_duration = int(self.runtime_params["rest_duration"].get())

            # 验证时间格式
            self.validate_time_format(self.runtime_params["morning_start"].get())
            self.validate_time_format(self.runtime_params["morning_end"].get())
            self.validate_time_format(self.runtime_params["afternoon_start"].get())
            self.validate_time_format(self.runtime_params["afternoon_end"].get())

            # 验证逻辑关系
            if interval_min >= interval_max:
                raise ValueError("最小间隔必须小于最大间隔")
            if daily_limit <= 0:
                raise ValueError("每日添加上限必须大于0")
            if max_per_window < 0:
                raise ValueError("每窗口最大添加数量不能为负数")
            if rest_trigger <= 0:
                raise ValueError("休息触发条件必须大于0")
            if rest_duration <= 0:
                raise ValueError("休息时长必须大于0")

            # 如果有控制器，立即应用参数
            if self.controller:
                self.apply_params_to_controller()

            return True

        except ValueError as e:
            raise ValueError(f"参数验证失败: {e}")

    def validate_time_format(self, time_str):
        """验证时间格式"""
        try:
            parts = time_str.split(':')
            if len(parts) != 2:
                raise ValueError(f"时间格式错误: {time_str}")

            hour = int(parts[0])
            minute = int(parts[1])

            if not (0 <= hour <= 23):
                raise ValueError(f"小时必须在0-23之间: {hour}")
            if not (0 <= minute <= 59):
                raise ValueError(f"分钟必须在0-59之间: {minute}")

        except ValueError:
            raise ValueError(f"时间格式错误: {time_str}，正确格式为 HH:MM")

    def apply_params_to_controller(self):
        """将参数应用到控制器"""
        if not self.controller:
            return

        try:
            # 更新控制器的配置
            config_updates = {
                "runtime_parameters": {
                    "single_add_interval": {
                        "min": int(self.runtime_params["interval_min"].get()),
                        "max": int(self.runtime_params["interval_max"].get())
                    },
                    "daily_add_limit": {
                        "value": int(self.runtime_params["daily_limit"].get())
                    },
                    "max_adds_per_window": {
                        "value": int(self.runtime_params["max_per_window"].get())
                    },
                    "execution_time_slots": {
                        "morning": {
                            "start": self.runtime_params["morning_start"].get(),
                            "end": self.runtime_params["morning_end"].get()
                        },
                        "afternoon": {
                            "start": self.runtime_params["afternoon_start"].get(),
                            "end": self.runtime_params["afternoon_end"].get()
                        }
                    }
                },
                "auto_rest_config": {
                    "rest_trigger": {
                        "friends_count": int(self.runtime_params["rest_trigger"].get())
                    },
                    "rest_duration": {
                        "minutes": int(self.runtime_params["rest_duration"].get())
                    }
                }
            }

            # 更新控制器配置
            if hasattr(self.controller, 'config_manager'):
                for key, value in config_updates.items():
                    self.controller.config_manager.set(key, value)

            self.log_message("INFO", "运行参数已实时更新到控制器")

        except Exception as e:
            self.log_message("ERROR", f"应用参数到控制器失败: {e}")

    def validate_runtime_params(self):
        """验证运行参数"""
        try:
            self.validate_and_apply_params()
            messagebox.showinfo("验证成功", "所有运行参数验证通过！")
            self.log_message("INFO", "运行参数验证成功")
        except Exception as e:
            messagebox.showerror("验证失败", str(e))
            self.log_message("ERROR", f"运行参数验证失败: {e}")

    def save_runtime_params(self):
        """保存运行参数到配置文件"""
        try:
            # 先验证参数
            self.validate_and_apply_params()

            # 读取现有配置
            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            # 更新配置
            config["runtime_parameters"] = {
                "single_add_interval": {
                    "min": int(self.runtime_params["interval_min"].get()),
                    "max": int(self.runtime_params["interval_max"].get()),
                    "unit": "seconds",
                    "description": "单次添加间隔范围"
                },
                "daily_add_limit": {
                    "value": int(self.runtime_params["daily_limit"].get()),
                    "description": "每日添加上限"
                },
                "max_adds_per_window": {
                    "value": int(self.runtime_params["max_per_window"].get()),
                    "description": "每窗口最大添加数量，0表示不限制"
                },
                "execution_time_slots": {
                    "morning": {
                        "start": self.runtime_params["morning_start"].get(),
                        "end": self.runtime_params["morning_end"].get(),
                        "enabled": self.time_slot_enabled["morning_enabled"].get(),
                        "description": "上午执行时段"
                    },
                    "afternoon": {
                        "start": self.runtime_params["afternoon_start"].get(),
                        "end": self.runtime_params["afternoon_end"].get(),
                        "enabled": self.time_slot_enabled["afternoon_enabled"].get(),
                        "description": "下午执行时段"
                    }
                }
            }

            config["auto_rest_config"] = {
                "rest_trigger": {
                    "friends_count": int(self.runtime_params["rest_trigger"].get()),
                    "description": "每添加X个好友后休息"
                },
                "rest_duration": {
                    "minutes": int(self.runtime_params["rest_duration"].get()),
                    "description": "休息时长（分钟）"
                },
                "enabled": True
            }

            # 保存配置
            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("保存成功", "运行参数已保存到配置文件！")
            self.log_message("INFO", "运行参数已保存到配置文件")

        except Exception as e:
            messagebox.showerror("保存失败", f"保存运行参数失败: {e}")
            self.log_message("ERROR", f"保存运行参数失败: {e}")

    def reset_runtime_params(self):
        """重置运行参数为默认值"""
        if messagebox.askyesno("确认重置", "确定要重置所有运行参数为默认值吗？"):
            try:
                # 重置为默认值
                self.runtime_params["interval_min"].set("50")
                self.runtime_params["interval_max"].set("60")
                self.runtime_params["daily_limit"].set("200")
                self.runtime_params["max_per_window"].set("20")
                self.runtime_params["morning_start"].set("10:00")
                self.runtime_params["morning_end"].set("12:00")
                self.runtime_params["afternoon_start"].set("14:00")
                self.runtime_params["afternoon_end"].set("23:59")
                self.runtime_params["rest_trigger"].set("20")
                self.runtime_params["rest_duration"].set("5")

                # 应用参数
                self.validate_and_apply_params()

                messagebox.showinfo("重置成功", "运行参数已重置为默认值！")
                self.log_message("INFO", "运行参数已重置为默认值")

            except Exception as e:
                messagebox.showerror("重置失败", f"重置运行参数失败: {e}")
                self.log_message("ERROR", f"重置运行参数失败: {e}")

    # ==================== 状态显示更新方法 ====================

    def update_status_display(self):
        """更新状态显示"""
        try:
            # 更新总进度
            total_contacts = self.execution_stats.get("total_contacts", 0)
            processed_contacts = self.execution_stats.get("processed_contacts", 0)

            if total_contacts > 0:
                progress_percent = (processed_contacts / total_contacts) * 100
                self.status_vars["total_progress"].set(f"{processed_contacts}/{total_contacts} ({progress_percent:.1f}%)")
                self.main_progress_bar.config(value=progress_percent)
            else:
                self.status_vars["total_progress"].set("0/0 (0%)")
                self.main_progress_bar.config(value=0)

            # 更新详细统计
            self.status_vars["planned_count"].set(str(total_contacts))
            self.status_vars["current_progress"].set(str(processed_contacts))
            self.status_vars["success_count"].set(str(self.execution_stats.get("successful_adds", 0)))
            self.status_vars["error_count"].set(str(self.execution_stats.get("failed_adds", 0)))

            # 更新窗口信息
            total_windows = self.execution_stats.get("total_windows", 0)
            completed_windows = self.execution_stats.get("completed_windows", 0)
            current_window = completed_windows + 1 if completed_windows < total_windows else total_windows
            self.status_vars["current_window"].set(f"{current_window}/{total_windows}")

            # 更新倒计时（这里可以根据实际需要实现）
            self.update_countdown()

        except Exception as e:
            self.log_message("ERROR", f"更新状态显示失败: {e}")
        finally:
            # 每秒更新一次状态显示
            self.root.after(1000, self.update_status_display)

    def update_countdown(self):
        """更新操作倒计时"""
        try:
            # 这里可以根据实际的操作间隔来计算倒计时
            # 暂时显示一个示例倒计时
            if self.is_running:
                # 获取当前配置的间隔时间
                interval_min = int(self.runtime_params["interval_min"].get())
                interval_max = int(self.runtime_params["interval_max"].get())
                avg_interval = (interval_min + interval_max) / 2

                # 这里应该根据实际的下次操作时间来计算
                # 暂时显示平均间隔时间作为示例
                import random
                countdown = random.randint(1, int(avg_interval))
                self.status_vars["countdown"].set(f"{countdown}秒")
            else:
                self.status_vars["countdown"].set("0秒")

        except Exception:
            self.status_vars["countdown"].set("N/A")

    def load_runtime_params_from_config(self):
        """从配置文件加载运行参数"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            # 加载运行参数
            runtime_params = config.get("runtime_parameters", {})
            if runtime_params:
                interval_config = runtime_params.get("single_add_interval", {})
                self.runtime_params["interval_min"].set(str(interval_config.get("min", 50)))
                self.runtime_params["interval_max"].set(str(interval_config.get("max", 60)))

                daily_limit = runtime_params.get("daily_add_limit", {})
                self.runtime_params["daily_limit"].set(str(daily_limit.get("value", 200)))

                max_per_window = runtime_params.get("max_adds_per_window", {})
                self.runtime_params["max_per_window"].set(str(max_per_window.get("value", 20)))

                time_slots = runtime_params.get("execution_time_slots", {})
                morning = time_slots.get("morning", {})
                afternoon = time_slots.get("afternoon", {})

                self.runtime_params["morning_start"].set(morning.get("start", "10:00"))
                self.runtime_params["morning_end"].set(morning.get("end", "12:00"))
                self.runtime_params["afternoon_start"].set(afternoon.get("start", "14:00"))
                self.runtime_params["afternoon_end"].set(afternoon.get("end", "23:59"))

                # 加载时段启用状态
                self.time_slot_enabled["morning_enabled"].set(morning.get("enabled", True))
                self.time_slot_enabled["afternoon_enabled"].set(afternoon.get("enabled", True))

                # 更新输入框状态
                self.on_morning_enabled_change()
                self.on_afternoon_enabled_change()

            # 加载自动休息配置
            rest_config = config.get("auto_rest_config", {})
            if rest_config:
                rest_trigger = rest_config.get("rest_trigger", {})
                self.runtime_params["rest_trigger"].set(str(rest_trigger.get("friends_count", 20)))

                rest_duration = rest_config.get("rest_duration", {})
                self.runtime_params["rest_duration"].set(str(rest_duration.get("minutes", 5)))

            self.log_message("INFO", "运行参数已从配置文件加载")

        except Exception as e:
            self.log_message("WARNING", f"加载运行参数失败，使用默认值: {e}")

    def adjust_numeric_value(self, var, delta, min_val, max_val):
        """调整数值变量的值"""
        try:
            current_val = int(var.get())
            new_val = max(min_val, min(max_val, current_val + delta))
            var.set(str(new_val))
            self.on_param_change()
        except ValueError:
            # 如果当前值不是有效数字，设置为最小值
            var.set(str(min_val))
            self.on_param_change()

    def adjust_float_value(self, var, delta, min_val, max_val):
        """调整浮点数变量的值"""
        try:
            current_val = float(var.get())
            new_val = max(min_val, min(max_val, current_val + delta))
            var.set(f"{new_val:.1f}")
            self.on_param_change()
        except ValueError:
            # 如果当前值不是有效数字，设置为最小值
            var.set(f"{min_val:.1f}")
            self.on_param_change()

    def adjust_time_value(self, var, delta_minutes):
        """调整时间变量的值（以分钟为单位）"""
        try:
            current_time = var.get()
            # 解析当前时间
            if ':' in current_time:
                hours, minutes = map(int, current_time.split(':'))
            else:
                # 如果格式不正确，设置为默认时间
                hours, minutes = 10, 0

            # 计算总分钟数
            total_minutes = hours * 60 + minutes + delta_minutes

            # 确保时间在有效范围内（00:00-23:59）
            total_minutes = max(0, min(1439, total_minutes))  # 1439 = 23*60 + 59

            # 转换回小时和分钟
            new_hours = total_minutes // 60
            new_minutes = total_minutes % 60

            # 格式化时间字符串
            new_time = f"{new_hours:02d}:{new_minutes:02d}"
            var.set(new_time)
            self.on_param_change()

        except (ValueError, AttributeError):
            # 如果当前值不是有效时间格式，设置为默认时间
            var.set("10:00")
            self.on_param_change()

    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


class GUILogHandler(logging.Handler):
    """自定义日志处理器，将日志发送到GUI"""

    def __init__(self, message_queue):
        super().__init__()
        self.message_queue = message_queue

    def emit(self, record):
        try:
            msg = self.format(record)
            self.message_queue.put(('log', record.levelname, msg))
        except Exception:
            self.handleError(record)


def main():
    """主程序入口"""
    try:
        # 创建并运行GUI
        app = WeChatAutomationGUI()
        app.run()
    except Exception as e:
        print(f"启动GUI失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
